package jnpf.database.plugins;

import jnpf.database.config.SqlLogConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;

/**
 * MyBatis SQL日志拦截器
 * 用于将执行的SQL语句和参数输出到控制台
 * 
 * <AUTHOR>
 * @version V3.1.0
 * @copyright 引迈信息技术有限公司
 * @date 2021/3/16 8:53
 */
@Slf4j
@Component
@Intercepts({
    @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
    @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class SqlLogInterceptor implements Interceptor {

    @Autowired(required = false)
    private SqlLogConfig sqlLogConfig;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行原方法
            Object result = invocation.proceed();
            
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executeTime = endTime - startTime;
            
            // 获取SQL信息并打印
            printSqlInfo(invocation, executeTime);
            
            return result;
        } catch (Exception e) {
            // 即使出现异常也要打印SQL信息
            long endTime = System.currentTimeMillis();
            long executeTime = endTime - startTime;
            printSqlInfo(invocation, executeTime);
            throw e;
        }
    }

    /**
     * 打印SQL信息
     */
    private void printSqlInfo(Invocation invocation, long executeTime) {
        try {
            // 如果配置为不启用或者未达到慢SQL阈值，则不输出
            if (sqlLogConfig != null && (!sqlLogConfig.isEnabled() ||
                (sqlLogConfig.getSlowSqlThreshold() > 0 && executeTime < sqlLogConfig.getSlowSqlThreshold()))) {
                return;
            }

            Object[] args = invocation.getArgs();
            MappedStatement ms = (MappedStatement) args[0];
            Object parameter = args[1];

            BoundSql boundSql = ms.getBoundSql(parameter);
            Configuration configuration = ms.getConfiguration();

            // 输出SQL日志到控制台
            System.out.println("================== MyBatis-Plus SQL Log ==================");
            System.out.println("==> Preparing: " + boundSql.getSql());

            if (sqlLogConfig == null || sqlLogConfig.isShowParameters()) {
                System.out.println("==> Parameters: " + getParameterValues(configuration, boundSql));
            }

            if (sqlLogConfig == null || sqlLogConfig.isShowCompleteSql()) {
                String sql = getSqlWithParameters(configuration, boundSql);
                System.out.println("==> Complete SQL: " + sql);
            }

            if (sqlLogConfig == null || sqlLogConfig.isShowExecuteTime()) {
                System.out.println("==> Execute Time: " + executeTime + " ms");
            }

            if (sqlLogConfig == null || sqlLogConfig.isShowMapper()) {
                System.out.println("==> Mapper: " + ms.getId());
            }

            System.out.println("=========================================================");

            // 同时输出到日志文件
            log.debug("SQL执行 - Mapper: {}", ms.getId());
            log.debug("SQL执行 - 原始SQL: {}", boundSql.getSql());
            if (sqlLogConfig == null || sqlLogConfig.isShowCompleteSql()) {
                String sql = getSqlWithParameters(configuration, boundSql);
                log.debug("SQL执行 - 完整SQL: {}", sql);
            }
            log.debug("SQL执行 - 执行时间: {} ms", executeTime);

        } catch (Exception e) {
            log.warn("打印SQL日志时发生异常", e);
        }
    }

    /**
     * 获取带参数的完整SQL语句
     */
    private String getSqlWithParameters(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        
        if (parameterMappings.size() > 0 && parameterObject != null) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?", getParameterValue(parameterObject));
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (ParameterMapping parameterMapping : parameterMappings) {
                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        sql = sql.replaceFirst("\\?", getParameterValue(obj));
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        Object obj = boundSql.getAdditionalParameter(propertyName);
                        sql = sql.replaceFirst("\\?", getParameterValue(obj));
                    }
                }
            }
        }
        return sql;
    }

    /**
     * 获取参数值列表字符串
     */
    private String getParameterValues(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        StringBuilder params = new StringBuilder();
        
        if (parameterMappings.size() > 0 && parameterObject != null) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                params.append(getParameterValue(parameterObject));
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                for (int i = 0; i < parameterMappings.size(); i++) {
                    ParameterMapping parameterMapping = parameterMappings.get(i);
                    String propertyName = parameterMapping.getProperty();
                    Object value = null;
                    
                    if (metaObject.hasGetter(propertyName)) {
                        value = metaObject.getValue(propertyName);
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        value = boundSql.getAdditionalParameter(propertyName);
                    }
                    
                    if (i > 0) {
                        params.append(", ");
                    }
                    params.append(getParameterValue(value));
                }
            }
        }
        return params.toString();
    }

    /**
     * 格式化参数值
     */
    private String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format(new Date()) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "null";
            }
        }
        return value;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可以在这里设置一些配置属性
    }
}
