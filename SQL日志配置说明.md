# MyBatis-Plus SQL日志配置说明

## 概述
本配置为JNPF项目添加了MyBatis-Plus SQL日志输出功能，可以将执行的SQL语句、参数和执行时间输出到控制台，方便开发调试。

## 配置文件修改

### 1. 主配置文件 (application.yml)
在 `jnpf-java-boot/jnpf-admin/src/main/resources/application.yml` 中添加了以下配置：

```yaml
# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    # MyBatis-Plus SQL日志输出配置
    jnpf: debug
    # Druid连接池SQL日志
    druid.sql.Statement: debug

# JNPF SQL日志配置
jnpf:
  sql:
    log:
      enabled: true # 是否启用SQL日志输出到控制台
      show-execute-time: true # 是否显示SQL执行时间
      show-complete-sql: true # 是否显示完整的SQL语句（包含参数值）
      show-parameters: true # 是否显示参数列表
      show-mapper: true # 是否显示Mapper信息
      slow-sql-threshold: 0 # 慢SQL阈值（毫秒），0表示所有SQL都输出
```

### 2. 工作流模块配置 (jnpf-workflow/jnpf-workflow-admin/src/main/resources/application.yml)
```yaml
logging:
  config: classpath:logback-spring.xml
  level:
    # MyBatis-Plus SQL日志输出配置
    jnpf: debug
    # Druid连接池SQL日志
    druid.sql.Statement: debug
```

## 新增文件

### 1. SQL日志拦截器
- 文件路径: `jnpf-common/jnpf-boot-common/jnpf-common-database/src/main/java/jnpf/database/plugins/SqlLogInterceptor.java`
- 功能: 拦截MyBatis SQL执行，输出详细的SQL信息到控制台

### 2. SQL日志配置类
- 文件路径: `jnpf-common/jnpf-boot-common/jnpf-common-database/src/main/java/jnpf/database/config/SqlLogConfig.java`
- 功能: 提供SQL日志的配置选项

## 修改的文件

### 1. MybatisPlusConfig.java
- 文件路径: `jnpf-common/jnpf-boot-common/jnpf-common-database/src/main/java/jnpf/database/config/MybatisPlusConfig.java`
- 修改内容: 添加了SqlLogInterceptor到MyBatis插件列表中

## 功能特性

### 1. 控制台输出格式
```
================== MyBatis-Plus SQL Log ==================
==> Preparing: SELECT id, name, age FROM user WHERE id = ?
==> Parameters: 1
==> Complete SQL: SELECT id, name, age FROM user WHERE id = 1
==> Execute Time: 15 ms
==> Mapper: com.example.mapper.UserMapper.selectById
=========================================================
```

### 2. 配置选项说明
- `enabled`: 是否启用SQL日志输出，生产环境建议设置为false
- `show-execute-time`: 是否显示SQL执行时间
- `show-complete-sql`: 是否显示完整的SQL语句（包含参数值）
- `show-parameters`: 是否显示参数列表
- `show-mapper`: 是否显示Mapper信息
- `slow-sql-threshold`: 慢SQL阈值，只有执行时间超过该值的SQL才会输出（0表示所有SQL都输出）

### 3. 环境配置建议
- **开发环境**: 所有选项都设置为true，threshold设置为0
- **测试环境**: 可以设置threshold为100ms，只输出慢SQL
- **生产环境**: 建议设置enabled为false，或者threshold设置为较大值（如1000ms）

## 使用方法

1. 启动应用后，所有的SQL执行都会在控制台输出详细信息
2. 可以通过修改配置文件中的参数来控制输出内容
3. 如果需要临时关闭SQL日志，可以将`jnpf.sql.log.enabled`设置为false

## 注意事项

1. SQL日志输出会影响性能，生产环境请谨慎使用
2. 包含敏感信息的SQL参数会被完整输出，请注意安全性
3. 大量的SQL输出可能会影响控制台的可读性
4. 建议在生产环境中使用慢SQL检测功能，只输出执行时间较长的SQL

## 故障排除

如果SQL日志没有输出，请检查：
1. 日志级别是否设置为debug
2. SqlLogConfig配置是否正确
3. 是否有其他日志配置覆盖了当前设置
